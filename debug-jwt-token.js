/**
 * Debug script to analyze Supabase JWT tokens
 * Run this in browser console when you have a session to see token structure
 */

// Function to decode JWT token (without verification)
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const header = JSON.parse(atob(parts[0]));
    const payload = JSON.parse(atob(parts[1]));
    
    return {
      header,
      payload,
      signature: parts[2]
    };
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

// Function to analyze current session token
async function analyzeCurrentToken() {
  // This assumes you're running in browser with Supabase client available
  if (typeof window === 'undefined') {
    console.log('This script should be run in the browser console');
    return;
  }
  
  try {
    // Try to get session from localStorage or sessionStorage
    const supabaseSession = localStorage.getItem('sb-qpkllizwchhppvqsnagl-auth-token');
    
    if (!supabaseSession) {
      console.log('No Supabase session found in localStorage');
      return;
    }
    
    const sessionData = JSON.parse(supabaseSession);
    const accessToken = sessionData.access_token;
    
    if (!accessToken) {
      console.log('No access token found in session');
      return;
    }
    
    console.log('=== JWT TOKEN ANALYSIS ===');
    console.log('Raw token (first 50 chars):', accessToken.substring(0, 50) + '...');
    
    const decoded = decodeJWT(accessToken);
    if (decoded) {
      console.log('\n=== JWT HEADER ===');
      console.log(JSON.stringify(decoded.header, null, 2));
      
      console.log('\n=== JWT PAYLOAD ===');
      console.log(JSON.stringify(decoded.payload, null, 2));
      
      console.log('\n=== TOKEN INFO ===');
      console.log('Issuer:', decoded.payload.iss);
      console.log('Subject (User ID):', decoded.payload.sub);
      console.log('Email:', decoded.payload.email);
      console.log('Role:', decoded.payload.role);
      console.log('Audience:', decoded.payload.aud);
      console.log('Expires:', new Date(decoded.payload.exp * 1000).toISOString());
      console.log('Issued:', new Date(decoded.payload.iat * 1000).toISOString());
      
      console.log('\n=== USER METADATA ===');
      console.log(JSON.stringify(decoded.payload.user_metadata, null, 2));
      
      console.log('\n=== APP METADATA ===');
      console.log(JSON.stringify(decoded.payload.app_metadata, null, 2));
    }
    
  } catch (error) {
    console.error('Error analyzing token:', error);
  }
}

// Function to test backend API call
async function testBackendCall() {
  try {
    const supabaseSession = localStorage.getItem('sb-qpkllizwchhppvqsnagl-auth-token');
    
    if (!supabaseSession) {
      console.log('No session found');
      return;
    }
    
    const sessionData = JSON.parse(supabaseSession);
    const accessToken = sessionData.access_token;
    
    console.log('=== TESTING BACKEND API CALL ===');
    console.log('Making request to: https://ai-nav.onrender.com/auth/sync-profile');
    
    const response = await fetch('https://ai-nav.onrender.com/auth/sync-profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (!response.ok) {
      console.error('API call failed');
      try {
        const errorData = JSON.parse(responseText);
        console.error('Error details:', errorData);
      } catch (e) {
        console.error('Could not parse error response as JSON');
      }
    } else {
      console.log('API call successful!');
      try {
        const data = JSON.parse(responseText);
        console.log('Response data:', data);
      } catch (e) {
        console.log('Response is not JSON');
      }
    }
    
  } catch (error) {
    console.error('Error testing backend call:', error);
  }
}

// Instructions
console.log(`
=== JWT DEBUG SCRIPT ===

To use this script:
1. Open browser console on your AI Navigator site
2. Make sure you're logged in with Google
3. Run: analyzeCurrentToken()
4. Run: testBackendCall()

Available functions:
- analyzeCurrentToken() - Analyze your current JWT token
- testBackendCall() - Test the backend API call
- decodeJWT(token) - Decode any JWT token
`);

// Auto-run if in browser
if (typeof window !== 'undefined') {
  console.log('JWT Debug script loaded. Run analyzeCurrentToken() to start.');
}
